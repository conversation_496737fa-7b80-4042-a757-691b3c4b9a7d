import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    TextInput,
    TouchableOpacity,
    ScrollView,
    Modal,
    FlatList,
    Alert,
    ActivityIndicator,
} from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';
import Icon from 'react-native-vector-icons/Feather';
import DateTimePicker from '@react-native-community/datetimepicker';
import Navbar from '../../components/Navbar';
import CheckBox from '@react-native-community/checkbox';
import { fetchWastageReasonList } from '../../apiHandling/StockAPI/fetchWastageReasonListAPI';
import { fetchWastageItems } from '../../apiHandling/StockAPI/fetchWastageItemsAPI';
import { fetchItemDetail, fetchItemStock } from '../../apiHandling/StockAPI/itemDetailsStockAPI';
import { saveWastage, fetchBusinessDate } from '../../apiHandling/StockAPI/saveWastageAPI';
import { fetchWastageList } from '../../apiHandling/StockAPI/fetchWastageListAPI';
import { fetchWastageDetail } from '../../apiHandling/StockAPI/fetchWastageDetailAPI';

import AsyncStorage from '@react-native-async-storage/async-storage';

const ports = [
    { label: 'COM 1', value: 'COM 1' },
    { label: 'COM 2', value: 'COM 2' },
    { label: 'COM 3', value: 'COM 3' },
];

const WastageScreen = ({ navigation }) => {
    // Wastage type related state
    const [wastageReasonList, setWastageReasonList] = useState([]);
    const [selectedWastageReason, setSelectedWastageReason] = useState(null);
    const [wastageItems, setWastageItems] = useState([]);

    // Item selection and details
    const [itemName, setItemName] = useState('');
    const [itemId, setItemId] = useState('');
    const [itemDetail, setItemDetail] = useState(null);
    const [modalVisible, setModalVisible] = useState(false);
    const [searchText, setSearchText] = useState('');

    // View modal state
    const [viewModalVisible, setViewModalVisible] = useState(false);
    const [wastageList, setWastageList] = useState([]);
    const [filteredWastages, setFilteredWastages] = useState([]);
    const [searchQuery, setSearchQuery] = useState('');
    const [fromDate, setFromDate] = useState(null);
    const [toDate, setToDate] = useState(null);
    const [showFromDatePicker, setShowFromDatePicker] = useState(false);
    const [showToDatePicker, setShowToDatePicker] = useState(false);
    const [loading, setLoading] = useState(false);

    // Item properties
    const [batchEnabled, setBatchEnabled] = useState(false);
    const [sellByWeight, setSellByWeight] = useState(false);
    const [altQtyEnabled, setAltQtyEnabled] = useState(false);

    // Form fields
    const [port, setPort] = useState(null);
    const [kgs, setKgs] = useState('');
    const [nos, setNos] = useState('');
    const [remarks, setRemarks] = useState('');
    const [batch, setBatch] = useState('');
    const [batchList, setBatchList] = useState([]);
    const [stockQty, setStockQty] = useState('');

    // Table and editing
    const [items, setItems] = useState([]);
    const [selectedItems, setSelectedItems] = useState([]);
    const [editingItemId, setEditingItemId] = useState(null);
    const [isEditing, setIsEditing] = useState(false);

    // Edit functionality states (following stock take pattern)
    const [editingItemIndex, setEditingItemIndex] = useState(null);


    useEffect(() => {
        loadWastageReasonList();
    }, []);

    // Load wastage list when view modal opens
    useEffect(() => {
        if (viewModalVisible) {
            loadWastageList();
        }
    }, [viewModalVisible]);

    // Filter wastages by date when dates change
    useEffect(() => {
        const fetchWastagesBySelectedDates = async () => {
            if (!viewModalVisible) return;

            setLoading(true);
            try {
                const bearerToken = await AsyncStorage.getItem('authToken');
                const selectedBranch = await AsyncStorage.getItem('selectedBranch');
                const parsedBranch = JSON.parse(selectedBranch);
                const loginBranchID = parsedBranch.BranchId;

                const fromDateStr = fromDate.toISOString().split('T')[0].replace(/-/g, '');
                const toDateStr = toDate.toISOString().split('T')[0].replace(/-/g, '');

                const wastages = await fetchWastageList(bearerToken, loginBranchID, fromDateStr, toDateStr);

                // Sort by DocID in increasing order
                const sortedWastages = wastages.sort((a, b) => {
                    const docIdA = parseInt(a.DocID) || 0;
                    const docIdB = parseInt(b.DocID) || 0;
                    return docIdA - docIdB;
                });

                setWastageList(sortedWastages);
                setFilteredWastages(sortedWastages);
            } catch (err) {
                console.error('Error fetching wastages by date:', err);
            }
            setLoading(false);
        };

        fetchWastagesBySelectedDates();
    }, [fromDate, toDate, viewModalVisible]);

    // Filter wastages when search query changes
    useEffect(() => {
        if (!searchQuery) {
            setFilteredWastages(wastageList);
        } else {
            const filtered = wastageList.filter(wastage =>
                wastage.DocID.toLowerCase().includes(searchQuery.toLowerCase()) ||
                wastage.ISO_Number.toLowerCase().includes(searchQuery.toLowerCase())
            );
            setFilteredWastages(filtered);
        }
    }, [searchQuery, wastageList]);

    const loadWastageReasonList = async () => {
        const bearerToken = await AsyncStorage.getItem('authToken');
        const data = await fetchWastageReasonList(bearerToken);
        setWastageReasonList(data);
    };

    const loadWastageList = async () => {
        setLoading(true);
        try {
            const bearerToken = await AsyncStorage.getItem('authToken');
            const selectedBranch = await AsyncStorage.getItem('selectedBranch');
            const parsedBranch = JSON.parse(selectedBranch);
            const loginBranchID = parsedBranch.BranchId;

            const businessDate = await fetchBusinessDate(bearerToken, loginBranchID);
            const businessDateObj = new Date(businessDate);
            const from = new Date(businessDateObj);
            const to = new Date(businessDateObj);
            to.setDate(to.getDate() + 1); // Next day

            setFromDate(from);
            setToDate(to);

            const fromDateStr = from.toISOString().split('T')[0].replace(/-/g, '');
            const toDateStr = to.toISOString().split('T')[0].replace(/-/g, '');

            const wastages = await fetchWastageList(bearerToken, loginBranchID, fromDateStr, toDateStr);

            // Sort by DocID in increasing order
            const sortedWastages = wastages.sort((a, b) => {
                const docIdA = parseInt(a.DocID) || 0;
                const docIdB = parseInt(b.DocID) || 0;
                return docIdA - docIdB;
            });

            setWastageList(sortedWastages);
            setFilteredWastages(sortedWastages);
        } catch (err) {
            console.error('Error loading wastage list:', err);
        }
        setLoading(false);
    };

    // Handle wastage selection and navigate to view screen
    const handleWastageSelect = (wastageId) => {
        setViewModalVisible(false);
        navigation.navigate('ViewWastageScreen', { wastageId });
    };

    const openViewModal = async () => {
        setLoading(true);
        const bearerToken = await AsyncStorage.getItem('authToken');
        const selectedBranch = await AsyncStorage.getItem('selectedBranch');
        const parsedBranch = JSON.parse(selectedBranch);
        const loginBranchID = parsedBranch.BranchId;

        try {
            const businessDate = await fetchBusinessDate(bearerToken, loginBranchID);
            const businessDateObj = new Date(businessDate);
            const from = new Date(businessDateObj);
            const to = new Date(businessDateObj);
            to.setDate(to.getDate() + 1); // Next day

            setFromDate(from);
            setToDate(to);

            const fromDateStr = from.toISOString().split('T')[0].replace(/-/g, '');
            const toDateStr = to.toISOString().split('T')[0].replace(/-/g, '');

            const wastages = await fetchWastageList(bearerToken, loginBranchID, fromDateStr, toDateStr);

            // Sort by DocID in increasing order
            const sortedWastages = wastages.sort((a, b) => {
                const docIdA = parseInt(a.DocID) || 0;
                const docIdB = parseInt(b.DocID) || 0;
                return docIdA - docIdB;
            });

            setWastageList(sortedWastages);
            setFilteredWastages(sortedWastages);
            setViewModalVisible(true);
        } catch (err) {
            console.error('Error fetching wastages:', err);
        }
        setLoading(false);
    };

    // Function to handle item row click for editing (following stock take pattern)
    const handleItemRowClick = (item, index) => {
        setEditingItemIndex(index);
        // Set wastage reason from item
        const wastageReason = wastageReasonList.find(reason => reason.value === item.wastageReasonId);
        if (wastageReason) {
            setSelectedWastageReason(wastageReason);
        }
        setItemName(item.itemName);
        setItemId(item.itemId);
        setBatch(item.batch);
        setStockQty(item.stockQty);
        setNos(item.nos.toString());
        setKgs(item.kgs.toString());
        setSellByWeight(item.sellByWeight);
        setAltQtyEnabled(item.altQtyEnabled);
        setBatchEnabled(item.batchEnabled);
        setPort(item.port);
        setItemDetail(item.itemDetail);
        setRemarks(item.remarks || '');
    };

    const handleWastageReasonChange = async (item) => {
        setSelectedWastageReason(item);
        // Clear previous item selection when wastage reason changes
        setItemName('');
        setItemId('');
        setItemDetail(null);
        setBatch('');
        setBatchList([]);
        setStockQty('');
        setNos('');
        setKgs('');
        const bearerToken = await AsyncStorage.getItem('authToken');


        // Load wastage items for selected reason
        if (item.WastageReasonID) {
            const items = await fetchWastageItems(bearerToken, item.WastageReasonID);
            setWastageItems(items);
        }
    };

    const handleSelectPress = () => {
        if (!selectedWastageReason) {
            Alert.alert('Error', 'Please select a wastage type first');
            return;
        }

        // Reset all item-related state
        setItemName('');
        setItemId('');
        setBatch('');
        setBatchList([]);
        setStockQty('');
        setNos('');
        setKgs('');
        setBatchEnabled(false);
        setSellByWeight(false);
        setAltQtyEnabled(false);
        setSearchText('');

        // Open item selection modal
        setModalVisible(true);
    };

    const handleItemSelect = async (item) => {
        setItemName(item.ItemName);
        setItemId(item.ItemID);
        setModalVisible(false);
        const bearerToken = await AsyncStorage.getItem('authToken');
        const selectedBranch = await AsyncStorage.getItem('selectedBranch');
        const parsedBranch = JSON.parse(selectedBranch);
        const loginBranchID = parsedBranch.BranchId;

        const detail = await fetchItemDetail(bearerToken, item.ItemID);
        setItemDetail(detail);

        if (!detail) {
            // Default assumption: batch is disabled, sell by weight is false, alt qty is false
            setBatchEnabled(false);
            setSellByWeight(false);
            setAltQtyEnabled(false);

            // Set stock and fields to 0
            setStockQty('0');
            setNos('0');
            setKgs('0');
            setBatchList([]);
            setBatch('');
            return;
        }

        const {
            BatchEnabled,
            SellByWeight,
            AltQtyEnabled,
        } = detail;

        setBatchEnabled(BatchEnabled);
        setSellByWeight(SellByWeight);
        setAltQtyEnabled(AltQtyEnabled);

        // Fetch stock data for the selected item
        const stockData = await fetchItemStock(bearerToken, loginBranchID, item.ItemID);
        console.log("Stock Data:", stockData);

        if (BatchEnabled) {
            // For batch enabled items, set batch list and reset fields
            setBatchList(stockData);
            setBatch('');
            setStockQty('0');
        } else if (Array.isArray(stockData) && stockData.length > 0) {
            // For non-batch items, use the first stock entry
            const stock = stockData[0];
            const qty = stock.StockQty || 0;
            setStockQty(qty.toString());
        } else {
            // No stock data available
            setStockQty('0');
        }

        // Set input fields based on item properties
        if (SellByWeight && AltQtyEnabled) {
            setKgs('');
            setNos('');
        } else if (SellByWeight) {
            setKgs('');
            setNos('0'); // Disable nos field
        } else {
            setNos('');
            setKgs('0'); // Disable kgs field
        }
    };

    const handleBatchSelect = (selectedBatch) => {
        setBatch(selectedBatch.BatchNumber || '');
        const qty = selectedBatch.StockQty || 0;
        setStockQty(qty.toString());
    };


    const handleAddItem = () => {
        // Validation
        if (!selectedWastageReason) return Alert.alert('Error', 'Please select a wastage type');
        if (!itemId || !itemName) return Alert.alert('Error', 'Please select an item');

        // Validate based on item properties
        if (sellByWeight && !kgs) return Alert.alert('Error', 'Enter Weight (Kg)');
        if (!sellByWeight && !altQtyEnabled && !nos) return Alert.alert('Error', 'Enter Nos');
        if (sellByWeight && altQtyEnabled && (!kgs || !nos)) return Alert.alert('Error', 'Enter both Nos and Weight');
        if (batchEnabled && !batch) return Alert.alert('Error', 'Please select a batch');

        // Stock quantity validation
        const currentStockQty = parseFloat(stockQty) || 0;
        const enteredNos = parseFloat(nos) || 0;
        const enteredKgs = parseFloat(kgs) || 0;

        if (sellByWeight && enteredKgs > currentStockQty) {
            return Alert.alert('Error', `Weight cannot exceed stock quantity (${currentStockQty} kg)`);
        }
        if (!sellByWeight && enteredNos > currentStockQty) {
            return Alert.alert('Error', `Nos cannot exceed stock quantity (${currentStockQty})`);
        }
        if (sellByWeight && altQtyEnabled && enteredKgs > currentStockQty) {
            return Alert.alert('Error', `Weight cannot exceed stock quantity (${currentStockQty} kg)`);
        }

        if (editingItemIndex !== null) {
            // Update existing item (following stock take pattern)
            const updatedItems = [...items];
            updatedItems[editingItemIndex] = {
                ...updatedItems[editingItemIndex],
                itemId,
                itemName,
                batch,
                port,
                stockQty,
                nos,
                kgs,
                remarks,
                wastageReasonId: selectedWastageReason.WastageReasonID,
                wastageReasonCode: selectedWastageReason.WastageReasonCode,
                wastageReasonName: selectedWastageReason.WastageReasonName,
                batchEnabled,
                sellByWeight,
                altQtyEnabled,
                itemDetail
            };
            setItems(updatedItems);
            setEditingItemIndex(null);
        } else {
            // Check for duplicates (only when adding new item)
            const isDuplicate = items.some(
                (entry) => entry.itemId === itemId && entry.batch === batch
            );
            if (isDuplicate) return Alert.alert('Error', 'Item already added with same batch');
            // Add new item
            const newItem = {
                id: Date.now().toString(),
                lineNo: String(items.length + 1).padStart(3, '0'),
                itemId,
                itemName,
                batch,
                port,
                stockQty,
                nos,
                kgs,
                remarks,
                wastageReasonId: selectedWastageReason.WastageReasonID,
                wastageReasonCode: selectedWastageReason.WastageReasonCode,
                wastageReasonName: selectedWastageReason.WastageReasonName,
                batchEnabled,
                sellByWeight,
                altQtyEnabled
            };
            setItems([...items, newItem]);
        }

        // Clear form fields
        handleClear();
    };

    const handleEditItem = (item) => {
        setItemId(item.itemId);
        setItemName(item.itemName);
        setBatch(item.batch);
        setPort(item.port);
        setStockQty(item.stockQty);
        setNos(item.nos);
        setKgs(item.kgs);
        setRemarks(item.remarks);
        setBatchEnabled(item.batchEnabled);
        setSellByWeight(item.sellByWeight);
        setAltQtyEnabled(item.altQtyEnabled);
        setEditingItemId(item.id);
        setIsEditing(true);
    };

    const handleClear = () => {
        setItemName('');
        setItemId('');
        setBatch('');
        setBatchList([]);
        setPort(null);
        setStockQty('');
        setNos('');
        setKgs('');
        setRemarks('');
        setIsEditing(false);
        setEditingItemId(null);
        setEditingItemIndex(null); // Reset editing state (following stock take pattern)
        setItemDetail(null);
        setBatchEnabled(false);
        setSellByWeight(false);
        setAltQtyEnabled(false);
    };

    const handleResetAndReload = () => {
        // Reset all form fields
        setItemName('');
        setItemId('');
        setBatch('');
        setBatchList([]);
        setPort(null);
        setStockQty('');
        setNos('');
        setKgs('');
        setRemarks('');
        setIsEditing(false);
        setEditingItemId(null);
        setItemDetail(null);
        setBatchEnabled(false);
        setSellByWeight(false);
        setAltQtyEnabled(false);
        setSearchText('');

        // Reset wastage reason and items
        setSelectedWastageReason(null);
        setWastageItems([]);

        // Reset table data
        setItems([]);
        setSelectedItems([]);

        // Reset modal states
        setModalVisible(false);

        // Reload wastage reason list
        loadWastageReasonList();
    };

    const toggleItemSelection = (id) => {
        if (selectedItems.includes(id)) {
            setSelectedItems(selectedItems.filter(itemId => itemId !== id));
        } else {
            setSelectedItems([...selectedItems, id]);
        }
    };

    const deleteSelectedItems = () => {
        const filtered = items.filter(item => !selectedItems.includes(item.id));
        const reIndexed = filtered.map((item, index) => ({
            ...item,
            lineNo: String(index + 1).padStart(3, '0'),
        }));
        setItems(reIndexed);
        setSelectedItems([]);
    };
    const getISTISOString = () => {
        const now = new Date();

        // Offset IST (+5:30) in milliseconds
        const istOffset = 5.5 * 60 * 60 * 1000;
        const istTime = new Date(now.getTime() + istOffset);

        // Format as ISO string (will still end in Z, but value is IST)
        return istTime.toISOString(); // Includes milliseconds and Z
    };

    const handleSave = async () => {

        const bearerToken = await AsyncStorage.getItem('authToken');
        const selectedBranch = await AsyncStorage.getItem('selectedBranch');
        const parsedBranch = JSON.parse(selectedBranch);
        const loginBranchID = parsedBranch.BranchId;

        const userDetails = await AsyncStorage.getItem('userData');
        const user = JSON.parse(userDetails);
        const loginUserID = user.userId;
        try {
            // Validation
            if (items.length === 0) {
                Alert.alert('Error', 'Please add at least one item');
                return;
            }

            const businessDate = await fetchBusinessDate(bearerToken, loginBranchID);
            const systemDate = getISTISOString();

            const payload = {
                wastageId: '',
                branchId: loginBranchID,
                transTypeId: 'SALE',
                businessDate: businessDate,
                remarks: '',
                isO_number: '',
                deleted: '',
                posted: true,
                wScale: 0,
                createdUserId: loginUserID,
                createdDate: systemDate,
                modifiedUserId: '',
                modifiedDate: '1753-01-01T00:00:00.000Z',
                deletedUserId: '',
                deletedDate: '1753-01-01T00:00:00.000Z',
                postedUserID: '',
                postedDate: '1753-01-01T00:00:00.000Z',
                wastageDetails: items.map(item => {
                    // Determine qty based on sellByWeight and altQtyEnabled
                    let qty = 0;
                    if (item.sellByWeight && item.altQtyEnabled) {
                        // Both enabled, use kgs as primary
                        qty = parseFloat(item.kgs || '0');
                    } else if (item.sellByWeight) {
                        // Only weight enabled
                        qty = parseFloat(item.kgs || '0');
                    } else {
                        // Only nos enabled
                        qty = parseFloat(item.nos || '0');
                    }

                    return {
                        lineNumber: item.lineNo,
                        wastageReasonName: item.wastageReasonName,
                        itemName: item.itemName,
                        batchNumber: item.batch || '',
                        nos: item.nos || '0',
                        kgs: item.kgs || '0',
                        stockQty: parseFloat(item.stockQty || '0'),
                        stockAltQty: 0, // Not used in wastage
                        altQtyEnabled: item.altQtyEnabled,
                        sellByWeight: item.sellByWeight,
                        altQty: parseFloat(item.nos || '0'), // Alt qty is nos
                        qty: qty,
                        groupStockQty: 0,
                        stockGroupId: itemDetail?.StockGroupId || '',
                        itemStatusId: 'OK',
                        binID: 'OKBIN',
                        itemID: item.itemId,
                        deleted: 'N',
                        wScale: 0,
                        wastageReasonID: item.wastageReasonId,
                        batchEnabled: item.batchEnabled,
                        createdUserId: loginUserID,
                        createdDate: systemDate,
                        modifiedUserId: '',
                        modifiedDate: '1753-01-01T00:00:00.000Z',
                        deletedUserId: '',
                        deletedDate: '1753-01-01T00:00:00.000Z',
                        dml: 'i'
                    };
                })
            };

            const result = await saveWastage(bearerToken, payload);

            if (result.result === 1) {
                // Show success dialog with print option (following stock take pattern)
                Alert.alert(
                    'Success',
                    `Wastage saved successfully\nWastage ID: ${result.description}\n\nDo you want to print the wastage?`,
                    [
                        {
                            text: 'No',
                            onPress: () => {
                                handleResetAndReload();
                            },
                            style: 'cancel'
                        },
                        {
                            text: 'Yes',
                            onPress: () => {
                                // Reset page first, then navigate
                                handleResetAndReload();
                                fetchLatestWastageAndNavigate(result.description);
                            }
                        }
                    ]
                );
            } else {
                Alert.alert('Error', 'Save Failed: Unexpected response from server.');
            }
        } catch (error) {
            console.error('Save error:', error);
            Alert.alert('Error', 'Failed to save wastage data.');
        }
    };

    // Function to fetch latest wastage and navigate to view screen (following stock take pattern)
    const fetchLatestWastageAndNavigate = async (wastageId) => {
        try {
            const selectedBranch = await AsyncStorage.getItem('selectedBranch');
            const parsedBranch = JSON.parse(selectedBranch);
            const loginBranchID = parsedBranch.BranchId;

            // Navigate to ViewWastageScreen with the saved wastage details
            navigation.navigate('ViewWastageScreen', {
                wastageId: wastageId
            });
        } catch (error) {
            console.error('Error navigating to view wastage:', error);
            Alert.alert('Error', 'Failed to navigate to view wastage');
            handleResetAndReload();
        }
    };

    return (
        <View style={styles.container}>
            <Navbar />
            <ScrollView contentContainerStyle={styles.scrollContent}>

                {/* Header and other sections remain the same... */}
                {/* Header */}
                <View style={styles.headerRow}>
                
                    <Text style={styles.headerTitle}>WASTAGE</Text>
                    <View style={styles.actionButtons}>
                        <TouchableOpacity style={styles.newBtn} onPress={handleResetAndReload}>
                            <Icon name="user-plus" size={16} color="#000" />
                            <Text style={styles.actionText}> New</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.viewBtn} onPress={openViewModal}>
                            <Icon name="eye" size={16} color="#000" />
                            <Text style={styles.actionText}> View</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            style={[styles.saveBtn, items.length === 0 && styles.saveBtnDisabled]}
                            onPress={items.length > 0 ? handleSave : null}
                            disabled={items.length === 0}
                        >
                            <Text style={[styles.saveText, items.length === 0 && styles.saveTextDisabled]}>Save</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.cancelBtn} onPress={handleResetAndReload}>
                            <Text style={styles.cancelText}>Cancel</Text>
                        </TouchableOpacity>
                    </View>
                </View>

                {/* WASTAGE DETAILS */}
                <View style={styles.section}>
                    <Text style={styles.sectionTitle}>WASTAGE DETAILS</Text>

                    {/* Wastage Type Selection */}
                    <View style={styles.inputRow}>
                        <View style={styles.dropdownWrapper}>
                            <Dropdown
                                style={styles.dropdown}
                                data={wastageReasonList}
                                labelField="WastageReasonName"
                                valueField="WastageReasonID"
                                placeholder="Select Wastage Type"
                                value={selectedWastageReason?.WastageReasonID}
                                onChange={handleWastageReasonChange}
                                selectedTextStyle={styles.selectedTextStyle}
                                placeholderStyle={styles.placeholderStyle}
                            />
                        </View>
                    </View>

                    <View style={styles.inputRow}>
                        <TextInput
                            placeholder="Item Name"
                            style={styles.inputBox}
                            value={itemName}
                            onChangeText={setItemName}
                        />
                        <TouchableOpacity style={styles.selectBtn} onPress={handleSelectPress}>
                            <Text style={styles.btnTextWhite}>Select</Text>
                        </TouchableOpacity>

                        <TextInput
                            placeholder="Choose Batch"
                            style={[styles.inputBox, !batchEnabled && styles.disabledInput]}
                            value={batch}
                            onChangeText={setBatch}
                            editable={batchEnabled}
                        />
                        <TouchableOpacity
                            style={[styles.selectBtn, !batchEnabled && styles.disabledBtn]}
                            onPress={batchEnabled ? handleBatchSelect : null}
                            disabled={!batchEnabled}
                        >
                            <Text style={styles.btnTextWhite}>Select</Text>
                        </TouchableOpacity>


                    </View>

                    <View style={styles.inputRow}>
                        <TextInput
                            placeholder="Stock Qty"
                            style={styles.inputSmall}
                            keyboardType="numeric"
                            value={stockQty}
                            onChangeText={setStockQty}
                        />


                        <View style={styles.dropdownWrapperPort}>
                            <Dropdown
                                style={styles.dropdownPort}
                                data={ports}
                                labelField="label"
                                valueField="value"
                                placeholder="Select Scale"
                                value={port}
                                onChange={item => setPort(item.value)}
                                selectedTextStyle={styles.selectedTextStyle}
                                placeholderStyle={styles.placeholderStyle}
                            />
                        </View>



                        <TextInput
                            placeholder="Nos"
                            style={[styles.inputSmall, (!sellByWeight || altQtyEnabled) ? {} : styles.disabledInput]}
                            keyboardType="numeric"
                            value={nos}
                            onChangeText={setNos}
                            editable={!sellByWeight || altQtyEnabled}
                        />

                        <TextInput
                            placeholder="Wt(Kg)"
                            style={[styles.inputSmall, sellByWeight ? {} : styles.disabledInput]}
                            keyboardType="numeric"
                            value={kgs}
                            onChangeText={setKgs}
                            editable={sellByWeight}
                        />
                    </View>

                    <View style={styles.row}>
                        <TextInput
                            placeholder="Remarks"
                            style={styles.remarkInput}
                            value={remarks}
                            onChangeText={setRemarks}
                        />
                        <TouchableOpacity
                            style={isEditing ? styles.updateBtn : styles.addBtn}
                            onPress={handleAddItem}
                        >
                            <Text style={styles.btnText}>{isEditing ? 'Update' : 'Add'}</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.clearBtn} onPress={handleClear}>
                            <Text style={styles.btnText}>Clear</Text>
                        </TouchableOpacity>
                    </View>

                </View>



                {/* Table Section */}
                <View style={styles.section}>
                    <View style={styles.tableContainer}>
                        <View style={styles.tableHeader}>
                            <Text style={[styles.headerCell, styles.checkboxCell]}>Select</Text>
                            <Text style={[styles.headerCell, styles.lineNumberCell]}>Line No.</Text>
                            <Text style={[styles.headerCell, styles.wastageReasonCell]}>Wastage Reason</Text>
                            <Text style={[styles.headerCell, styles.itemNameCell]}>Item Name</Text>
                            <Text style={[styles.headerCell, styles.batchCell]}>Batch Number</Text>
                            <Text style={[styles.headerCell, styles.nosCell]}>Nos</Text>
                            <Text style={[styles.headerCell, styles.kgsCell]}>Kgs</Text>
                            <Text style={[styles.headerCell, styles.stockQtyCell]}>Stock Qty</Text>
                            <Text style={[styles.headerCell, styles.actionCell]}>Action</Text>
                        </View>

                        <View style={styles.tableScrollContainer}>
                            <ScrollView>
                                {items.length === 0 ? (
                                    <View style={[styles.tableRow, { justifyContent: 'center' }]}>
                                        <Text style={{ color: '#999', paddingVertical: 20 }}>No items added yet</Text>
                                    </View>
                                ) : (
                                    items.map((item, index) => (
                                        <TouchableOpacity
                                            key={item.id}
                                            style={[
                                                styles.tableRow,
                                                index % 2 === 0 && styles.tableRowEven,
                                                editingItemIndex === index && styles.tableRowEditing
                                            ]}
                                            onPress={() => handleItemRowClick(item, index)}
                                        >
                                            <View style={[styles.cell, styles.checkboxCell]}>
                                                <CheckBox
                                                    value={selectedItems.includes(item.id)}
                                                    onValueChange={() => toggleItemSelection(item.id)}
                                                    tintColors={{ true: '#002b5c', false: '#002b5c' }}
                                                    style={{ transform: [{ scaleX: 1.4 }, { scaleY: 1.4 }] }}
                                                />
                                            </View>
                                            <Text style={[styles.cell, styles.lineNumberCell]}>{index + 1}</Text>
                                            <Text style={[styles.cell, styles.wastageReasonCell]}>{item.wastageReasonName}</Text>
                                            <Text style={[styles.cell, styles.itemNameCell]}>{item.itemName}</Text>
                                            <Text style={[styles.cell, styles.batchCell]}>{item.batch || '-'}</Text>
                                            <Text style={[styles.cell, styles.nosCell]}>{item.nos}</Text>
                                            <Text style={[styles.cell, styles.kgsCell]}>{item.kgs}</Text>
                                            <Text style={[styles.cell, styles.stockQtyCell]}>{item.stockQty}</Text>
                                            <View style={[styles.cell, styles.actionCell]}>
                                                <TouchableOpacity onPress={() => handleEditItem(item)}>
                                                    <Icon name="edit" size={30} color="green" />
                                                </TouchableOpacity>
                                            </View>
                                        </TouchableOpacity>
                                    ))
                                )}
                            </ScrollView>
                        </View>

                        {/* Table Footer */}
                        <View style={styles.footerRow}>
                            {/* Delete Button */}
                            <TouchableOpacity style={styles.deleteBtn} onPress={deleteSelectedItems}>
                                <Text style={styles.footerText}>Delete</Text>
                            </TouchableOpacity>
                        </View>

                    </View>
                </View>

                {/* Item Selection Modal (following stock take pattern) */}
                <Modal visible={modalVisible} transparent animationType="slide">
                    <View
                        style={{
                            flex: 1,
                            justifyContent: 'center',
                            alignItems: 'center',
                            backgroundColor: 'rgba(0, 0, 0, 0.5)',
                        }}
                    >
                        <View
                            style={{
                                width: '90%',
                                maxHeight: '80%',
                                backgroundColor: 'white',
                                borderRadius: 10,
                                padding: 16,
                                position: 'relative',
                            }}
                        >
                            {/* Header */}
                            <View
                                style={{
                                    flexDirection: 'row',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    marginBottom: 16,
                                }}
                            >
                                <Text style={{ fontSize: 18, fontWeight: 'bold' }}>Select Item</Text>
                            </View>

                            {/* Search Input */}
                            <TextInput
                                style={{
                                    borderWidth: 1,
                                    borderColor: '#ccc',
                                    borderRadius: 8,
                                    paddingHorizontal: 12,
                                    paddingVertical: 8,
                                    marginBottom: 16,
                                    fontSize: 16,
                                }}
                                placeholder="Search items..."
                                value={searchText}
                                onChangeText={setSearchText}
                            />

                            {/* Filtered ScrollView with 4-column layout (following stock take pattern) */}
                            <ScrollView contentContainerStyle={{
                                flexDirection: 'row',
                                flexWrap: 'wrap',
                                justifyContent: 'flex-start',
                                paddingVertical: 10,
                            }}>
                                {wastageItems.filter(
                                    (item) =>
                                        item.ItemName.toLowerCase().includes(searchText.toLowerCase()) ||
                                        item.ItemID.toLowerCase().includes(searchText.toLowerCase())
                                ).map((item) => (
                                    <TouchableOpacity
                                        key={item.ItemID}
                                        style={{
                                            backgroundColor: '#FDC500',
                                            borderRadius: 8,
                                            padding: 10,
                                            width: '23%',
                                            minHeight: 100,
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            marginBottom: 10,
                                            marginRight: '2%',
                                        }}
                                        onPress={() => handleItemSelect(item)}
                                    >
                                        <Text style={{
                                            fontSize: 14,
                                            color: 'rgb(2, 2, 2)',
                                            textAlign: 'center',
                                        }}>{item.ItemName}</Text>
                                    </TouchableOpacity>
                                ))}
                            </ScrollView>

                            {/* Close X Button (following stock take pattern) */}
                            <TouchableOpacity
                                onPress={() => setModalVisible(false)}
                                style={{
                                    position: 'absolute',
                                    top: 10,
                                    right: 10,
                                    width: 30,
                                    height: 30,
                                    backgroundColor: '#000',
                                    borderRadius: 15,
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    zIndex: 1000,
                                }}
                            >
                                <Text style={{ color: '#fff', fontSize: 18, fontWeight: 'bold' }}>×</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </Modal>

                {/* View Wastage Modal (following stock take design pattern) */}
                <Modal
                    animationType="slide"
                    transparent={true}
                    visible={viewModalVisible}
                    onRequestClose={() => setViewModalVisible(false)}
                >
                    <View style={styles.dialog_overlay}>
                        <View style={styles.dialog_container}>
                            {/* Title */}
                            <Text style={styles.dialog_title}>View Wastage</Text>

                            {/* Search and Date Controls in one row (following stock take pattern) */}
                            <View style={styles.dialog_searchDateContainer}>
                                {/* Search Input */}
                                <View style={styles.dialog_searchWrapper}>
                                    <TextInput
                                        placeholder="Search by Doc ID or ISO Number..."
                                        value={searchQuery}
                                        onChangeText={(text) => {
                                            setSearchQuery(text);
                                        }}
                                        style={styles.dialog_searchInput}
                                    />
                                </View>

                                {/* Date Pickers */}
                                <View style={styles.dialog_dateWrapper}>
                                    <View style={styles.dialog_dateGroup}>
                                        <Text style={styles.dialog_dateLabel}>From Date</Text>
                                        <TouchableOpacity
                                            onPress={() => setShowFromDatePicker(true)}
                                            style={styles.dialog_dateButton}
                                        >
                                            <Text style={styles.dialog_dateText}>
                                                {fromDate ? fromDate.toLocaleDateString() : 'Select'}
                                            </Text>
                                        </TouchableOpacity>
                                    </View>

                                    <View style={styles.dialog_dateGroup}>
                                        <Text style={styles.dialog_dateLabel}>To Date</Text>
                                        <TouchableOpacity
                                            onPress={() => setShowToDatePicker(true)}
                                            style={styles.dialog_dateButton}
                                        >
                                            <Text style={styles.dialog_dateText}>
                                                {toDate ? toDate.toLocaleDateString() : 'Select'}
                                            </Text>
                                        </TouchableOpacity>
                                    </View>
                                </View>
                            </View>

                            {/* From Date Picker */}
                            {showFromDatePicker && (
                                <DateTimePicker
                                    value={fromDate || new Date()}
                                    mode="date"
                                    display="default"
                                    onChange={(event, selectedDate) => {
                                        setShowFromDatePicker(false);
                                        if (selectedDate) {
                                            setFromDate(selectedDate);
                                        }
                                    }}
                                />
                            )}

                            {/* To Date Picker */}
                            {showToDatePicker && (
                                <DateTimePicker
                                    value={toDate || new Date()}
                                    mode="date"
                                    display="default"
                                    onChange={(event, selectedDate) => {
                                        setShowToDatePicker(false);
                                        if (selectedDate) {
                                            setToDate(selectedDate);
                                        }
                                    }}
                                />
                            )}

                            {/* Wastage List (following stock take 3-column grid pattern) */}
                            <ScrollView style={styles.dialog_content}>
                                <View style={styles.dialog_grid}>
                                    {loading && (
                                        <View style={styles.dialog_loadingContainer}>
                                            <ActivityIndicator size="large" color="#0000ff" />
                                        </View>
                                    )}
                                    {!loading && filteredWastages.map((wastage, index) => {
                                        // Calculate margin for proper 3-button layout (copied from stock take)
                                        const isThirdInRow = (index + 1) % 3 === 0;
                                        const marginRightValue = isThirdInRow ? 0 : '2%';

                                        return (
                                            <TouchableOpacity
                                                key={index}
                                                onPress={() => {
                                                    handleWastageSelect(wastage.WastageID);
                                                }}
                                                style={{
                                                    backgroundColor: '#FDC500',
                                                    padding: 15,
                                                    borderRadius: 12,
                                                    width: '32%',
                                                    aspectRatio: 1,
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                    marginBottom: 15,
                                                    marginRight: marginRightValue,
                                                    shadowColor: '#000',
                                                    shadowOffset: {
                                                        width: 0,
                                                        height: 2,
                                                    },
                                                    shadowOpacity: 0.25,
                                                    shadowRadius: 3.84,
                                                    elevation: 5,
                                                }}
                                            >
                                                <Text style={{
                                                    color: '#000',
                                                    textAlign: 'center',
                                                    fontWeight: 'bold',
                                                    fontSize: 14,
                                                }}>
                                                    {wastage.DocID}
                                                </Text>
                                            </TouchableOpacity>
                                        );
                                    })}
                                    {!loading && filteredWastages.length === 0 && (
                                        <View style={styles.dialog_loadingContainer}>
                                            <Text>No wastage records found for the selected date range.</Text>
                                        </View>
                                    )}
                                </View>
                            </ScrollView>

                            {/* Close X Button (following stock take pattern) */}
                            <TouchableOpacity
                                onPress={() => setViewModalVisible(false)}
                                style={styles.dialog_closeButton}
                            >
                                <Text style={styles.dialog_closeButtonText}>×</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </Modal>

            </ScrollView>
        </View>
    );
};


const styles = StyleSheet.create({

    container: {
        flex: 1,
        backgroundColor: '#F7F8F9'

    },
    scrollContent: {
        paddingBottom: 20,
    },

    headerRow: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 10,
        backgroundColor: '#e9e9e9',
        padding: 20,
    },

    headerTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        flex: 1,
        marginLeft: 10,
    },
    actionButtons: {
        flexDirection: 'row',
        gap: 5,
        flexWrap: 'wrap',
    },

    newBtn: {
        backgroundColor: '#E2E3E5',
        paddingVertical: 14,
        paddingHorizontal: 25,
        flexDirection: 'row',
        alignItems: 'center',
        borderRadius: 8,
    },
    viewBtn: {
        backgroundColor: '#E2E3E5',
        paddingVertical: 14,
        paddingHorizontal: 25,
        flexDirection: 'row',
        alignItems: 'center',
        borderRadius: 8,
    },
    saveBtn: {
        backgroundColor: '#28A745',
        paddingVertical: 14,
        paddingHorizontal: 25,
        borderRadius: 8,
    },
    saveBtnDisabled: {
        backgroundColor: '#cccccc',
    },

    cancelBtn: {
        backgroundColor: 'red',
        paddingVertical: 14,
        paddingHorizontal: 25,
        borderRadius: 8,
    },

    actionText: {
        fontWeight: 'bold',
        fontSize: 16,
    },
    saveText: {
        color: '#fff',
        fontWeight: 'bold',
        fontSize: 16,
    },
    saveTextDisabled: {
        color: '#999999',
    },
    cancelText: {
        color: '#fff',
        fontWeight: 'bold',
        fontSize: 16,
    },


    section: {
        backgroundColor: '#e9e9e9',
        paddingHorizontal: 10,
        marginBottom: 0,

    },
    sectionTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        marginBottom: 10,
        marginTop: 0, // ↓ Ensures no extra space on top
        color: '#000',
    },

    inputRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: 8,
        marginBottom: 12,
        width: '100%',
    },

    inputBox: {
        flex: 1,
        borderWidth: 1,
        borderColor: '#999',
        borderRadius: 6,
        paddingHorizontal: 10,
        height: 50, // Increased from 40
        backgroundColor: '#fff',
    },

    dropdownBox: {
        flex: 1,
        minWidth: '30%',
        borderWidth: 1,
        borderColor: '#aaa',
        borderRadius: 6,
        paddingHorizontal: 10,
        height: 50, // Increased from 40
        backgroundColor: '#fff',
    },

    dropdownWrapper: {
        flex: 1,
        minWidth: '30%',
    },

    dropdownWrapperPort: {
        width: 120,
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 6,
        backgroundColor: '#fff',
    },


    dropdown: {
        height: 50, // Increased from 40
        borderColor: '#aaa',
        borderWidth: 1,
        borderRadius: 6,
        paddingHorizontal: 8,
        backgroundColor: '#fff',
    },
    dropdownPort: {
        height: 50, // Increased from 40
        paddingHorizontal: 8,
    },
    selectedTextStyle: {
        fontSize: 14,
    },
    placeholderStyle: {
        fontSize: 14,
        color: '#888',
    },
    selectBtn: {
        paddingHorizontal: 12,
        paddingVertical: 12, // Increased
        backgroundColor: '#1E2D52',
        borderRadius: 6,
        height: 50, // Increased from 40
        justifyContent: 'center',
        alignItems: 'center',
    },
    transitBtn: {
        backgroundColor: '#1E2D52',
        paddingHorizontal: 12,
        paddingVertical: 12, // Increased
        borderRadius: 6,
        height: 50, // Increased from 40
        justifyContent: 'center',
        alignItems: 'center',
    },
    newBtnSmall: {
        backgroundColor: '#28A745',
        height: 50, // Increased from 40
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 12,
        borderRadius: 6,
        width: 70,
    },

    remarkInput: {
        height: 50,
        backgroundColor: '#fff',
        flex: 1,
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 5,
        paddingHorizontal: 10,
        marginRight: 8,
    },

    remarkInputlocation: {
        height: 50,
        backgroundColor: '#fff',
        flex: 1,
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 5,
        paddingHorizontal: 10,
    },


    addBtn: {
        backgroundColor: 'green',
        width: 90,
        height: 50,
        justifyContent: 'center',    // center vertically
        alignItems: 'center',        // center horizontally
        borderRadius: 5,
        marginRight: 10,             // gap between Add and Clear
    },

    clearBtn: {
        backgroundColor: 'red',
        width: 90,
        height: 50,
        justifyContent: 'center',    // center vertically
        alignItems: 'center',        // center horizontally
        borderRadius: 5,
    },

    btnText: {
        color: '#fff',
        fontWeight: 'bold',
        textAlign: 'center',
    },
    LocationclearBtn: {
        backgroundColor: 'red',
        paddingHorizontal: 12,
        paddingVertical: 12, // Increased
        height: 50, // Increased from 40
        borderRadius: 6,
        width: 70,
        justifyContent: 'center',
        alignItems: 'center',
    },

    inputSmall: {
        flex: 1,
        minWidth: '22%',
        height: 50, // Increased from 40
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 6,
        paddingHorizontal: 8,
        backgroundColor: '#fff',
    },
    btnText: {
        color: '#fff',
        fontWeight: 'bold',
        textAlign: 'center',
        fontSize: 14, // Added for better visibility
    },
    btnTextWhite: {
        color: '#fff',
        fontWeight: 'bold',
        fontSize: 14, // Added for better visibility
    },
    inputWithButton: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
        minWidth: '48%',
        gap: 8,
    },
    row: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },


    tableScrollContainer: {
        maxHeight: 240, // Adjust based on your row height. ~50px * 4 rows
        borderWidth: 1,
        borderColor: '#ccc',

    },

    tableContainer: {
        marginTop: 10,
        overflow: 'hidden',
        borderRadius: 5,
        marginBottom: 10,
    },
    tableHeader: {
        flexDirection: 'row',
        backgroundColor: '#002b5c',
        paddingVertical: 14,
        paddingHorizontal: 8,
        alignItems: 'center',
    },
    headerCell: {
        flex: 1,
        fontWeight: '600',
        color: 'white',
        fontSize: 14,
        textAlign: 'center',
    },
    tableRow: {
        flexDirection: 'row',
        paddingVertical: 12,
        borderBottomWidth: 1,
        borderColor: '#f0f0f0',
        alignItems: 'center',
        backgroundColor: '#fff',
    },
    tableRowEven: {
        backgroundColor: '#f9f9f9',
    },
    tableRowEditing: {
        backgroundColor: '#e6f3ff',
    },
    cell: {
        flex: 1,
        paddingHorizontal: 8,
        textAlign: 'center',
        fontSize: 13,
        color: '#333',
    },
    checkboxCell: {
        flex: 0.4,
        textAlign: 'center',
    },
    lineNumberCell: {
        flex: 0.6,
    },
    wastageReasonCell: {
        flex: 1.2,
        textAlign: 'left',
    },
    itemNameCell: {
        flex: 1.3,
        textAlign: 'left',
    },
    batchCell: {
        flex: 1.0,
        textAlign: 'center',
    },
    nosCell: {
        flex: 0.7,
    },
    kgsCell: {
        flex: 0.7,
    },
    stockQtyCell: {
        flex: 0.8,
    },


    footerRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 10,
        backgroundColor: '#fff',
        borderColor: '#ccc',

        flexWrap: 'wrap',
    },

    footerBox: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#dcdcdc',
        paddingHorizontal: 10,
        paddingVertical: 8,
        borderRadius: 6,
        height: 40,
        marginLeft: 8,
    },

    footerBoxHighlight: {
        backgroundColor: '#bcd4ff',
    },

    footerLabel: {
        color: '#333',
        fontWeight: '600',
    },

    footerLabelHighlight: {
        color: '#003f8a',
    },

    footerValue: {
        color: '#000',
        fontWeight: '600',
        marginLeft: 4,
    },

    deleteBtn: {
        backgroundColor: 'red',
        paddingHorizontal: 15,
        paddingVertical: 10,
        borderRadius: 6,
    },

    footerText: {
        color: '#fff',
        fontWeight: 'bold',
    },

    updateBtn: {
        backgroundColor: '#ffa500',
        width: 90,
        height: 50,
        justifyContent: 'center',    // center vertically
        alignItems: 'center',        // center horizontally
        borderRadius: 5,
        marginRight: 10,
    },
    actionCell: {
        width: 50,
        justifyContent: 'center',
        alignItems: 'center',
    },



    // Disabled styles
    disabledInput: {
        backgroundColor: '#f5f5f5',
        color: '#999',
    },
    disabledBtn: {
        backgroundColor: '#ccc',
    },

    // View modal styles (following stock take pattern)
    dialog_overlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    dialog_container: {
        backgroundColor: '#fff',
        borderRadius: 15,
        padding: 20,
        width: '90%',
        maxHeight: '80%',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
    dialog_title: {
        fontSize: 20,
        fontWeight: 'bold',
        textAlign: 'center',
        marginBottom: 20,
        color: '#333',
    },
    dialog_searchContainer: {
        marginBottom: 15,
    },
    dialog_searchInput: {
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        paddingHorizontal: 12,
        paddingVertical: 10,
        fontSize: 16,
        backgroundColor: '#f9f9f9',
        minHeight: 40,
    },
    dialog_searchDateContainer: {
        flexDirection: 'row',
        marginBottom: 20,
        gap: 10,
        alignItems: 'flex-end',
    },
    dialog_searchWrapper: {
        flex: 2,
    },
    dialog_dateWrapper: {
        flexDirection: 'row',
        gap: 10,
        flex: 3,
    },
    dialog_dateGroup: {
        flex: 1,
        alignItems: 'center',
    },
    dialog_dateLabel: {
        fontSize: 12,
        fontWeight: 'bold',
        marginBottom: 5,
        color: '#333',
        textAlign: 'center',
    },
    dialog_dateContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 20,
        gap: 10,
    },
    dialog_dateButton: {
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        paddingHorizontal: 12,
        paddingVertical: 10,
        alignItems: 'center',
        backgroundColor: '#f9f9f9',
        minHeight: 40,
    },
    dialog_dateText: {
        fontSize: 14,
        color: '#333',
    },
    dialog_content: {
        maxHeight: 300,
    },
    dialog_grid: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'flex-start',
        paddingHorizontal: 5,
    },
    dialog_loadingContainer: {
        width: '100%',
        alignItems: 'center',
        padding: 20,
    },
    dialog_closeButton: {
        position: 'absolute',
        top: 10,
        right: 10,
        width: 30,
        height: 30,
        backgroundColor: '#000',
        borderRadius: 15,
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 1000,
    },
    dialog_closeButtonText: {
        color: '#fff',
        fontSize: 18,
        fontWeight: 'bold',
    },
});

export default WastageScreen;